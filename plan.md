# PLAN DE DESARROLLO COMPLETO - SISTEMA DE CONTABILIDAD

## 1. INFORMACIÓN GENERAL DEL PROYECTO

**Tipo:** Aplicación web de contabilidad multi-tenant  
**Arquitectura:** Monolítica modular con capacidades multi-tenant  
**Objetivo:** Sistema completo de gestión contable y financiera para empresas

## 2. STACK TECNOLÓGICO COMPLETO

### 2.1 Backend Framework y Core
- **Laravel 10.0** - Framework PHP principal
- **PHP 8.1+** - Lenguaje de programación base
- **Eloquent ORM** - Mapeo objeto-relacional
- **Laravel Sanctum** - Autenticación API
- **Laratrust** - Sistema de roles y permisos

### 2.2 Frontend y UI
- **Vue.js 2.7.14** - Framework JavaScript reactivo
- **Element UI** - Biblioteca de componentes Vue
- **Tailwind CSS** - Framework CSS utility-first
- **Laravel Mix** - Compilación de assets
- **Webpack** - Bundler de módulos
- **Livewire 3.0** - Componentes dinámicos full-stack

### 2.3 Base de Datos y Persistencia
- **MySQL/MariaDB** - Base de datos principal
- **SQLite** - Base de datos para testing
- **Redis** - Cache y sesiones (opcional)
- **Laravel Mediable** - Gestión de archivos multimedia

### 2.4 Seguridad y Firewall
- **Laravel Firewall** - Protección avanzada contra amenazas
- **CORS** - Control de acceso entre dominios
- **CSRF Protection** - Protección contra ataques CSRF
- **XSS Protection** - Filtrado de scripts maliciosos
- **Rate Limiting** - Limitación de peticiones

### 2.5 Herramientas de Desarrollo
- **PHPUnit** - Testing framework
- **Laravel Telescope** - Debugging y profiling
- **Laravel Debugbar** - Barra de debug
- **Sentry** - Monitoreo de errores
- **GitHub Actions** - CI/CD

## 3. ARQUITECTURA DEL SISTEMA

### 3.1 Patrón Arquitectónico
```
┌─────────────────────────────────────────────────────────────┐
│                    ARQUITECTURA MULTI-TENANT                │
├─────────────────────────────────────────────────────────────┤
│  Frontend (Vue.js + Tailwind)                              │
├─────────────────────────────────────────────────────────────┤
│  API Layer (Laravel Sanctum + RESTful APIs)                │
├─────────────────────────────────────────────────────────────┤
│  Business Logic Layer (Controllers + Jobs + Services)       │
├─────────────────────────────────────────────────────────────┤
│  Data Access Layer (Eloquent Models + Repositories)        │
├─────────────────────────────────────────────────────────────┤
│  Database Layer (MySQL + Redis Cache)                      │
└─────────────────────────────────────────────────────────────┘
```

### 3.2 Estructura Modular
- **Core Application** - Funcionalidades base
- **Modules System** - Sistema de módulos extensibles
- **Plugin Architecture** - Arquitectura de plugins
- **Multi-Company Support** - Soporte multi-empresa

### 3.3 Patrones de Diseño Implementados
- **Repository Pattern** - Abstracción de acceso a datos
- **Factory Pattern** - Creación de objetos
- **Observer Pattern** - Eventos y listeners
- **Strategy Pattern** - Múltiples algoritmos de pago
- **Middleware Pattern** - Filtros de peticiones HTTP

## 4. ESQUEMA DE BASE DE DATOS COMPLETO

### 4.1 Tablas Principales

#### Gestión de Empresas y Usuarios
```sql
-- Empresas (Multi-tenancy)
companies: id, name, domain, enabled, settings, created_at, updated_at

-- Usuarios
users: id, name, email, password, locale, enabled, landing_page, created_at

-- Relación Usuario-Empresa
user_companies: user_id, company_id, created_at

-- Roles y Permisos (Laratrust)
roles: id, name, display_name, description, created_at
permissions: id, name, display_name, description, created_at
role_user: role_id, user_id, user_type, created_at
permission_user: permission_id, user_id, user_type, created_at
```

#### Sistema Contable Core
```sql
-- Cuentas Bancarias
accounts: id, company_id, name, number, currency_code, type, opening_balance, 
         bank_name, bank_phone, bank_address, enabled, created_at

-- Transacciones
transactions: id, company_id, type, account_id, document_id, contact_id, 
             description, amount, currency_code, currency_rate, reference, 
             payment_method, paid_at, created_at

-- Documentos (Facturas, Facturas de Compra, etc.)
documents: id, company_id, type, document_number, order_number, status, 
          issued_at, due_at, amount, currency_code, currency_rate, 
          contact_id, contact_name, contact_email, contact_address, 
          notes, footer, created_at

-- Items de Documentos
document_items: id, company_id, document_id, item_id, name, description, 
               quantity, price, total, tax, discount_rate, created_at
```

#### Gestión de Contactos y Productos
```sql
-- Contactos (Clientes/Proveedores)
contacts: id, company_id, type, name, email, tax_number, phone, address, 
         city, zip_code, state, country, website, currency_code, 
         enabled, reference, created_at

-- Items/Productos
items: id, company_id, name, description, sale_price, purchase_price, 
      category_id, tax_ids, enabled, created_at

-- Categorías
categories: id, company_id, name, type, color, enabled, parent_id, created_at
```

#### Configuración y Settings
```sql
-- Settings por Empresa
settings: id, company_id, key, value

-- Monedas
currencies: id, company_id, name, code, rate, precision, symbol, 
           symbol_first, decimal_mark, thousands_separator, enabled, created_at

-- Impuestos
taxes: id, company_id, name, rate, type, enabled, created_at

-- Módulos
modules: id, company_id, alias, enabled, created_from, created_by, created_at
```

### 4.2 Relaciones Principales
- **Company → Users** (Many-to-Many)
- **Company → Accounts** (One-to-Many)
- **Company → Documents** (One-to-Many)
- **Document → DocumentItems** (One-to-Many)
- **Document → Transactions** (One-to-Many)
- **Account → Transactions** (One-to-Many)
- **Contact → Documents** (One-to-Many)
- **Item → DocumentItems** (One-to-Many)

## 5. SISTEMA DE RUTAS Y API

### 5.1 Grupos de Middleware
```php
// Grupos de rutas con diferentes niveles de acceso
'web' => ['cookies', 'session', 'csrf', 'language', 'firewall']
'api' => ['auth.basic', 'throttle', 'permission:read-api', 'company.identify']
'admin' => ['web', 'auth', 'company.identify', 'permission:read-admin-panel']
'portal' => ['web', 'auth', 'company.identify', 'permission:read-client-portal']
'guest' => ['web', 'auth.redirect']
```

### 5.2 Rutas Principales

#### Rutas de Administración (Admin Panel)
```php
// Gestión de Empresas
GET|POST /admin/companies
GET|PUT|DELETE /admin/companies/{id}

// Gestión Financiera
GET|POST /admin/banking/accounts
GET|POST /admin/banking/transactions
GET|POST /admin/banking/transfers
GET|POST /admin/banking/reconciliations

// Documentos
GET|POST /admin/sales/invoices
GET|POST /admin/purchases/bills
GET|POST /admin/sales/revenues
GET|POST /admin/purchases/payments

// Configuración
GET|PUT /admin/settings/company
GET|PUT /admin/settings/localisation
GET|PUT /admin/settings/defaults
GET|PUT /admin/settings/email
```

#### API RESTful
```php
// API de Cuentas
GET /api/accounts - Lista de cuentas
POST /api/accounts - Crear cuenta
GET /api/accounts/{id} - Obtener cuenta
PUT /api/accounts/{id} - Actualizar cuenta
DELETE /api/accounts/{id} - Eliminar cuenta

// API de Documentos
GET /api/documents - Lista de documentos
POST /api/documents - Crear documento
GET /api/documents/{id} - Obtener documento
PUT /api/documents/{id} - Actualizar documento

// API de Contactos
GET /api/contacts - Lista de contactos
POST /api/contacts - Crear contacto
GET /api/contacts/{id} - Obtener contacto
```

#### Portal del Cliente
```php
// Dashboard del Cliente
GET /portal/dashboard

// Facturas del Cliente
GET /portal/invoices
GET /portal/invoices/{id}
POST /portal/invoices/{id}/payments

// Perfil del Cliente
GET /portal/profile
PUT /portal/profile
```

## 6. SISTEMA DE AUTENTICACIÓN Y AUTORIZACIÓN

### 6.1 Middleware de Seguridad
```php
// Identificación de Empresa (Multi-tenancy)
'company.identify' => IdentifyCompany::class

// Autenticación
'auth' => Authenticate::class
'auth.basic.once' => AuthenticateOnceWithBasicAuth::class

// Autorización (Laratrust)
'permission' => LaratrustPermission::class
'role' => LaratrustRole::class

// Seguridad
'firewall.all' => [Firewall múltiples capas]
'read.only' => CheckForReadOnlyMode::class
```

### 6.2 Sistema de Permisos
```php
// Permisos CRUD por Módulo
'create-{module}' => 'Crear registros'
'read-{module}' => 'Leer registros'
'update-{module}' => 'Actualizar registros'
'delete-{module}' => 'Eliminar registros'

// Permisos Especiales
'read-admin-panel' => 'Acceso al panel de administración'
'read-client-portal' => 'Acceso al portal del cliente'
'read-api' => 'Acceso a la API'
```

### 6.3 Firewall Avanzado
- **IP Filtering** - Filtrado por direcciones IP
- **Geo Blocking** - Bloqueo geográfico
- **Bot Detection** - Detección de bots
- **XSS Protection** - Protección contra XSS
- **SQL Injection Protection** - Protección contra inyección SQL
- **Rate Limiting** - Limitación de peticiones

## 7. DISEÑO FRONTEND Y INTERFAZ DE USUARIO

### 7.1 Sistema de Colores (Tailwind CSS)
```css
// Colores Principales
--green-primary: #6ea152 (Color principal del sistema)
--purple-primary: #55588b (Color secundario)
--red-primary: #cc0000 (Color de error/peligro)

// Paleta Verde (Color principal)
green-50: #f8faf6
green-100: #f1f6ee
green-200: #dbe8d4
green-300: #c5d9ba
green-400: #9abd86
green-500: #6ea152 (Principal)
green-600: #63914a
green-700: #53793e
green-800: #426131
green-900: #364f28

// Paleta Púrpura (Color secundario)
purple-50: #f7f7f9
purple-100: #eeeef3
purple-200: #d5d5e2
purple-300: #bbbcd1
purple-400: #888aae
purple-500: #55588b (Principal)
purple-600: #4d4f7d
purple-700: #404268
purple-800: #333553
purple-900: #2a2b44

// Colores Adicionales
light-gray: #C7C9D9
dark-blue: #15284B
lighter-gray: #F2F2F5
purple-lighter: #F2F4FC
modal-background: rgba(0, 0, 0, 0.3)
black-medium: #424242
red-light: #FF6B6B
```

### 7.2 Tipografía
```css
// Fuente Principal
font-family: 'Quicksand', sans-serif

// Tamaños de Fuente
text-xs: 0.75rem
text-sm: 0.875rem
text-base: 1rem
text-lg: 1.125rem
text-xl: 1.25rem
text-2xl: 1.5rem
text-3xl: 1.875rem
text-4xl: 2.25rem
```

### 7.3 Sistema de Iconos
```html
<!-- Material Icons (Principal) -->
<link rel="stylesheet" href="public/css/fonts/material-icons/style.css">

<!-- Iconos Comunes -->
dashboard, account_balance, receipt, people, settings,
business, payment, trending_up, description, folder,
add, edit, delete, visibility, download, upload,
search, filter_list, more_vert, close, check
```

### 7.4 Layouts Principales

#### Layout de Autenticación
```html
<!-- Estructura de Login/Registro -->
<div class="h-screen lg:h-auto bg-no-repeat bg-cover bg-center"
     style="background-image: url('public/img/auth/login-bg.png')">
    <div class="relative w-full lg:max-w-7xl flex items-center m-auto">
        <!-- Slider lateral con información -->
        <div class="slider-section">
            <!-- Contenido promocional -->
        </div>

        <!-- Formulario de autenticación -->
        <div class="auth-form-section">
            <!-- Formularios de login/registro -->
        </div>
    </div>
</div>
```

#### Layout Principal (Admin)
```html
<!-- Estructura del Panel de Administración -->
<div class="admin-layout">
    <!-- Sidebar Navigation -->
    <nav class="sidebar">
        <!-- Menú principal -->
    </nav>

    <!-- Main Content Area -->
    <main class="main-content">
        <!-- Header con breadcrumbs -->
        <header class="content-header">
            <!-- Título y acciones -->
        </header>

        <!-- Content Body -->
        <div class="content-body">
            <!-- Contenido principal -->
        </div>
    </main>
</div>
```

#### Layout del Portal Cliente
```html
<!-- Estructura del Portal del Cliente -->
<div class="portal-layout">
    <!-- Header simplificado -->
    <header class="portal-header">
        <!-- Logo y navegación básica -->
    </header>

    <!-- Content Area -->
    <main class="portal-content">
        <!-- Contenido del cliente -->
    </main>
</div>
```

### 7.5 Componentes Vue.js Principales

#### Componentes de Formulario
```javascript
// AkauntingSelect.vue - Select personalizado
<akaunting-select
    :options="options"
    :value="value"
    @input="onInput"
    :placeholder="placeholder"
    :disabled="disabled"
/>

// AkauntingDate.vue - Selector de fecha
<akaunting-date
    :value="date"
    @input="onDateChange"
    :format="dateFormat"
    :disabled="disabled"
/>

// AkauntingMoney.vue - Input de dinero
<akaunting-money
    :value="amount"
    @input="onAmountChange"
    :currency="currency"
    :disabled="disabled"
/>
```

#### Componentes de Modal
```javascript
// AkauntingModal.vue - Modal base
<akaunting-modal
    :show="showModal"
    @close="closeModal"
    :title="modalTitle"
    :size="modalSize"
>
    <!-- Contenido del modal -->
</akaunting-modal>

// AkauntingModalAddNew.vue - Modal para crear registros
<akaunting-modal-add-new
    :show="showAddModal"
    @close="closeAddModal"
    :form-url="createUrl"
    :form-data="formData"
/>
```

### 7.6 Estructura de Navegación

#### Menú Principal (Sidebar)
```html
<!-- Dashboard -->
<li class="menu-item">
    <a href="/dashboard" class="menu-link">
        <i class="material-icons">dashboard</i>
        <span>Panel de Control</span>
    </a>
</li>

<!-- Gestión Bancaria -->
<li class="menu-item has-submenu">
    <a href="#" class="menu-link">
        <i class="material-icons">account_balance</i>
        <span>Banca</span>
    </a>
    <ul class="submenu">
        <li><a href="/accounts">Cuentas</a></li>
        <li><a href="/transactions">Transacciones</a></li>
        <li><a href="/transfers">Transferencias</a></li>
        <li><a href="/reconciliations">Conciliaciones</a></li>
    </ul>
</li>

<!-- Ventas -->
<li class="menu-item has-submenu">
    <a href="#" class="menu-link">
        <i class="material-icons">receipt</i>
        <span>Ventas</span>
    </a>
    <ul class="submenu">
        <li><a href="/invoices">Facturas</a></li>
        <li><a href="/revenues">Ingresos</a></li>
        <li><a href="/customers">Clientes</a></li>
    </ul>
</li>

<!-- Compras -->
<li class="menu-item has-submenu">
    <a href="#" class="menu-link">
        <i class="material-icons">shopping_cart</i>
        <span>Compras</span>
    </a>
    <ul class="submenu">
        <li><a href="/bills">Facturas de Compra</a></li>
        <li><a href="/payments">Pagos</a></li>
        <li><a href="/vendors">Proveedores</a></li>
    </ul>
</li>

<!-- Productos y Servicios -->
<li class="menu-item">
    <a href="/items" class="menu-link">
        <i class="material-icons">inventory</i>
        <span>Productos</span>
    </a>
</li>

<!-- Reportes -->
<li class="menu-item has-submenu">
    <a href="#" class="menu-link">
        <i class="material-icons">trending_up</i>
        <span>Reportes</span>
    </a>
    <ul class="submenu">
        <li><a href="/reports/income-summary">Resumen de Ingresos</a></li>
        <li><a href="/reports/expense-summary">Resumen de Gastos</a></li>
        <li><a href="/reports/profit-loss">Pérdidas y Ganancias</a></li>
        <li><a href="/reports/balance-sheet">Balance General</a></li>
    </ul>
</li>

<!-- Configuración -->
<li class="menu-item has-submenu">
    <a href="#" class="menu-link">
        <i class="material-icons">settings</i>
        <span>Configuración</span>
    </a>
    <ul class="submenu">
        <li><a href="/settings/company">Empresa</a></li>
        <li><a href="/settings/localisation">Localización</a></li>
        <li><a href="/settings/defaults">Valores por Defecto</a></li>
        <li><a href="/settings/email">Email</a></li>
        <li><a href="/settings/categories">Categorías</a></li>
        <li><a href="/settings/currencies">Monedas</a></li>
        <li><a href="/settings/taxes">Impuestos</a></li>
    </ul>
</li>
```

### 7.7 Estructura de Tablas y Listados

#### Tabla Estándar
```html
<!-- Estructura de tabla con acciones -->
<div class="table-responsive">
    <table class="table table-striped">
        <thead class="table-header">
            <tr>
                <th class="sortable">
                    <a href="#" class="sort-link">
                        Nombre
                        <i class="material-icons sort-icon">arrow_upward</i>
                    </a>
                </th>
                <th>Email</th>
                <th>Estado</th>
                <th class="text-center">Acciones</th>
            </tr>
        </thead>
        <tbody>
            <tr class="table-row">
                <td class="font-medium">Nombre del Cliente</td>
                <td><EMAIL></td>
                <td>
                    <span class="badge badge-success">Activo</span>
                </td>
                <td class="text-center">
                    <div class="dropdown">
                        <button class="btn-action" data-toggle="dropdown">
                            <i class="material-icons">more_vert</i>
                        </button>
                        <div class="dropdown-menu">
                            <a href="#" class="dropdown-item">
                                <i class="material-icons">visibility</i>
                                Ver
                            </a>
                            <a href="#" class="dropdown-item">
                                <i class="material-icons">edit</i>
                                Editar
                            </a>
                            <a href="#" class="dropdown-item text-red">
                                <i class="material-icons">delete</i>
                                Eliminar
                            </a>
                        </div>
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
</div>

<!-- Paginación -->
<div class="pagination-wrapper">
    <nav class="pagination-nav">
        <ul class="pagination">
            <li class="page-item disabled">
                <span class="page-link">Anterior</span>
            </li>
            <li class="page-item active">
                <span class="page-link">1</span>
            </li>
            <li class="page-item">
                <a class="page-link" href="#">2</a>
            </li>
            <li class="page-item">
                <a class="page-link" href="#">Siguiente</a>
            </li>
        </ul>
    </nav>
</div>
```

### 7.8 Formularios y Campos

#### Estructura de Formulario
```html
<!-- Formulario estándar -->
<form class="form-horizontal" method="POST">
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">Información General</h3>
        </div>

        <div class="card-body">
            <!-- Campo de texto -->
            <div class="form-group row">
                <label class="col-sm-3 col-form-label">
                    Nombre <span class="text-red">*</span>
                </label>
                <div class="col-sm-9">
                    <input type="text"
                           class="form-control"
                           name="name"
                           placeholder="Ingrese el nombre"
                           required>
                    <div class="invalid-feedback">
                        Este campo es requerido
                    </div>
                </div>
            </div>

            <!-- Campo select -->
            <div class="form-group row">
                <label class="col-sm-3 col-form-label">Categoría</label>
                <div class="col-sm-9">
                    <akaunting-select
                        :options="categories"
                        :value="form.category_id"
                        @input="form.category_id = $event"
                        placeholder="Seleccione una categoría"
                    />
                </div>
            </div>

            <!-- Campo de dinero -->
            <div class="form-group row">
                <label class="col-sm-3 col-form-label">Precio</label>
                <div class="col-sm-9">
                    <akaunting-money
                        :value="form.price"
                        @input="form.price = $event"
                        :currency="defaultCurrency"
                    />
                </div>
            </div>

            <!-- Campo de fecha -->
            <div class="form-group row">
                <label class="col-sm-3 col-form-label">Fecha</label>
                <div class="col-sm-9">
                    <akaunting-date
                        :value="form.date"
                        @input="form.date = $event"
                        format="DD/MM/YYYY"
                    />
                </div>
            </div>

            <!-- Campo de archivo -->
            <div class="form-group row">
                <label class="col-sm-3 col-form-label">Adjunto</label>
                <div class="col-sm-9">
                    <div class="dropzone-wrapper">
                        <div class="dropzone-area">
                            <i class="material-icons">cloud_upload</i>
                            <p>Arrastra archivos aquí o haz clic para seleccionar</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="card-footer">
            <div class="row">
                <div class="col-sm-9 offset-sm-3">
                    <button type="submit" class="btn btn-success">
                        <i class="material-icons">save</i>
                        Guardar
                    </button>
                    <a href="#" class="btn btn-secondary">
                        <i class="material-icons">cancel</i>
                        Cancelar
                    </a>
                </div>
            </div>
        </div>
    </div>
</form>
```

### 7.9 Sistema de Botones

#### Botones Principales
```html
<!-- Botón primario -->
<button class="btn btn-success">
    <i class="material-icons">add</i>
    Crear Nuevo
</button>

<!-- Botón secundario -->
<button class="btn btn-secondary">
    <i class="material-icons">cancel</i>
    Cancelar
</button>

<!-- Botón de peligro -->
<button class="btn btn-danger">
    <i class="material-icons">delete</i>
    Eliminar
</button>

<!-- Botón de información -->
<button class="btn btn-info">
    <i class="material-icons">info</i>
    Información
</button>

<!-- Botón con loading -->
<button class="btn btn-success" :disabled="loading">
    <i class="material-icons" v-if="loading">hourglass_empty</i>
    <i class="material-icons" v-else>save</i>
    {{ loading ? 'Guardando...' : 'Guardar' }}
</button>
```

### 7.10 Sistema de Notificaciones

#### Tipos de Alertas
```html
<!-- Alerta de éxito -->
<div class="alert alert-success alert-dismissible">
    <button type="button" class="close" data-dismiss="alert">
        <i class="material-icons">close</i>
    </button>
    <i class="material-icons">check_circle</i>
    <strong>¡Éxito!</strong> La operación se completó correctamente.
</div>

<!-- Alerta de error -->
<div class="alert alert-danger alert-dismissible">
    <button type="button" class="close" data-dismiss="alert">
        <i class="material-icons">close</i>
    </button>
    <i class="material-icons">error</i>
    <strong>Error:</strong> Ocurrió un problema al procesar la solicitud.
</div>

<!-- Alerta de advertencia -->
<div class="alert alert-warning alert-dismissible">
    <button type="button" class="close" data-dismiss="alert">
        <i class="material-icons">close</i>
    </button>
    <i class="material-icons">warning</i>
    <strong>Advertencia:</strong> Revise los datos antes de continuar.
</div>

<!-- Alerta de información -->
<div class="alert alert-info alert-dismissible">
    <button type="button" class="close" data-dismiss="alert">
        <i class="material-icons">close</i>
    </button>
    <i class="material-icons">info</i>
    <strong>Información:</strong> Datos adicionales sobre la operación.
</div>
```

### 7.11 Dashboard y Widgets

#### Estructura del Dashboard
```html
<!-- Dashboard principal -->
<div class="dashboard-container">
    <!-- Métricas principales -->
    <div class="metrics-row">
        <div class="metric-card">
            <div class="metric-icon bg-green">
                <i class="material-icons">trending_up</i>
            </div>
            <div class="metric-content">
                <h3 class="metric-value">$25,430</h3>
                <p class="metric-label">Ingresos del Mes</p>
            </div>
        </div>

        <div class="metric-card">
            <div class="metric-icon bg-red">
                <i class="material-icons">trending_down</i>
            </div>
            <div class="metric-content">
                <h3 class="metric-value">$8,250</h3>
                <p class="metric-label">Gastos del Mes</p>
            </div>
        </div>

        <div class="metric-card">
            <div class="metric-icon bg-blue">
                <i class="material-icons">account_balance</i>
            </div>
            <div class="metric-content">
                <h3 class="metric-value">$45,680</h3>
                <p class="metric-label">Balance Total</p>
            </div>
        </div>

        <div class="metric-card">
            <div class="metric-icon bg-purple">
                <i class="material-icons">receipt</i>
            </div>
            <div class="metric-content">
                <h3 class="metric-value">127</h3>
                <p class="metric-label">Facturas Pendientes</p>
            </div>
        </div>
    </div>

    <!-- Gráficos y widgets -->
    <div class="widgets-row">
        <div class="widget-card">
            <div class="widget-header">
                <h4 class="widget-title">Flujo de Efectivo</h4>
                <div class="widget-actions">
                    <button class="btn-widget-action">
                        <i class="material-icons">more_vert</i>
                    </button>
                </div>
            </div>
            <div class="widget-body">
                <!-- Gráfico de líneas -->
                <canvas id="cashFlowChart"></canvas>
            </div>
        </div>

        <div class="widget-card">
            <div class="widget-header">
                <h4 class="widget-title">Facturas Recientes</h4>
            </div>
            <div class="widget-body">
                <!-- Lista de facturas -->
                <div class="recent-items">
                    <div class="recent-item">
                        <div class="item-info">
                            <span class="item-title">Factura #001</span>
                            <span class="item-subtitle">Cliente ABC</span>
                        </div>
                        <div class="item-amount">$1,250.00</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
```

## 8. SISTEMA DE DOCUMENTOS Y TEMPLATES

### 8.1 Templates de Documentos

#### Templates Disponibles
```php
// Configuración de Templates (config/type.php)
'templates' => [
    [
        'id' => 'default',
        'name' => 'Plantilla por Defecto',
        'image' => 'public/img/invoice_templates/default.png',
        'template' => 'default',
    ],
    [
        'id' => 'classic',
        'name' => 'Plantilla Clásica',
        'image' => 'public/img/invoice_templates/classic.png',
        'template' => 'classic',
    ],
    [
        'id' => 'modern',
        'name' => 'Plantilla Moderna',
        'image' => 'public/img/invoice_templates/modern.png',
        'template' => 'modern',
    ],
]
```

#### Estructura de Template de Factura
```html
<!-- resources/views/sales/invoices/print_default.blade.php -->
<div class="invoice-template">
    <!-- Header con logo y datos de empresa -->
    <div class="invoice-header">
        <div class="company-info">
            <img src="{{ company_logo() }}" alt="Logo" class="company-logo">
            <div class="company-details">
                <h2 class="company-name">{{ setting('company.name') }}</h2>
                <p class="company-address">{{ setting('company.address') }}</p>
                <p class="company-contact">
                    {{ setting('company.phone') }} | {{ setting('company.email') }}
                </p>
            </div>
        </div>

        <div class="invoice-info">
            <h1 class="invoice-title">FACTURA</h1>
            <div class="invoice-details">
                <p><strong>Número:</strong> {{ $invoice->document_number }}</p>
                <p><strong>Fecha:</strong> {{ $invoice->issued_at->format('d/m/Y') }}</p>
                <p><strong>Vencimiento:</strong> {{ $invoice->due_at->format('d/m/Y') }}</p>
            </div>
        </div>
    </div>

    <!-- Información del cliente -->
    <div class="customer-info">
        <div class="billing-address">
            <h3>Facturar a:</h3>
            <p class="customer-name">{{ $invoice->contact_name }}</p>
            <p class="customer-address">{{ $invoice->contact_address }}</p>
            <p class="customer-contact">{{ $invoice->contact_email }}</p>
        </div>
    </div>

    <!-- Tabla de items -->
    <div class="invoice-items">
        <table class="items-table">
            <thead>
                <tr>
                    <th>Descripción</th>
                    <th class="text-center">Cantidad</th>
                    <th class="text-right">Precio Unit.</th>
                    <th class="text-right">Total</th>
                </tr>
            </thead>
            <tbody>
                @foreach($invoice->items as $item)
                <tr>
                    <td>
                        <strong>{{ $item->name }}</strong>
                        @if($item->description)
                            <br><small>{{ $item->description }}</small>
                        @endif
                    </td>
                    <td class="text-center">{{ $item->quantity }}</td>
                    <td class="text-right">{{ money($item->price, $invoice->currency_code) }}</td>
                    <td class="text-right">{{ money($item->total, $invoice->currency_code) }}</td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    <!-- Totales -->
    <div class="invoice-totals">
        <table class="totals-table">
            <tr>
                <td>Subtotal:</td>
                <td class="text-right">{{ money($invoice->sub_total, $invoice->currency_code) }}</td>
            </tr>
            @foreach($invoice->totals as $total)
            <tr>
                <td>{{ $total->name }}:</td>
                <td class="text-right">{{ money($total->amount, $invoice->currency_code) }}</td>
            </tr>
            @endforeach
            <tr class="total-row">
                <td><strong>Total:</strong></td>
                <td class="text-right"><strong>{{ money($invoice->amount, $invoice->currency_code) }}</strong></td>
            </tr>
        </table>
    </div>

    <!-- Notas y términos -->
    @if($invoice->notes)
    <div class="invoice-notes">
        <h4>Notas:</h4>
        <p>{{ $invoice->notes }}</p>
    </div>
    @endif

    @if($invoice->footer)
    <div class="invoice-footer">
        <p>{{ $invoice->footer }}</p>
    </div>
    @endif
</div>
```

### 8.2 Sistema de Impresión y PDF

#### Generación de PDF
```php
// Trait Documents.php
public function storeDocumentPdfAndGetPath($document)
{
    event(new DocumentPrinting($document));

    $view = view($document->template_path, [
        'invoice' => $document,
        'document' => $document
    ])->render();

    $html = mb_convert_encoding($view, 'HTML-ENTITIES', 'UTF-8');

    $pdf = app('dompdf.wrapper');
    $pdf->loadHTML($html);

    $file_name = $this->getDocumentFileName($document);
    $pdf_path = get_storage_path('app/temp/' . $file_name);

    $pdf->save($pdf_path);

    return $pdf_path;
}
```

#### Descarga de PDF
```php
// Controller method
public function pdfInvoice(Document $invoice)
{
    event(new DocumentPrinting($invoice));

    $currency_style = true;

    $view = view($invoice->template_path, compact('invoice', 'currency_style'))->render();
    $html = mb_convert_encoding($view, 'HTML-ENTITIES', 'UTF-8');

    $pdf = app('dompdf.wrapper');
    $pdf->loadHTML($html);

    $file_name = $this->getDocumentFileName($invoice);

    return $pdf->download($file_name);
}
```

#### CSS para Impresión
```css
/* public/css/print.css */
@media print {
    body {
        font-family: 'Quicksand', sans-serif;
        font-size: 12px;
        line-height: 1.4;
        color: #333;
    }

    .invoice-template {
        max-width: 100%;
        margin: 0;
        padding: 20px;
    }

    .invoice-header {
        display: flex;
        justify-content: space-between;
        margin-bottom: 30px;
        border-bottom: 2px solid #6ea152;
        padding-bottom: 20px;
    }

    .company-logo {
        max-height: 80px;
        max-width: 200px;
    }

    .items-table {
        width: 100%;
        border-collapse: collapse;
        margin: 20px 0;
    }

    .items-table th,
    .items-table td {
        border: 1px solid #ddd;
        padding: 8px;
        text-align: left;
    }

    .items-table th {
        background-color: #f8f9fa;
        font-weight: bold;
    }

    .totals-table {
        width: 300px;
        margin-left: auto;
        margin-top: 20px;
    }

    .total-row {
        border-top: 2px solid #6ea152;
        font-weight: bold;
        font-size: 14px;
    }
}
```

### 8.3 Sistema de Exportación

#### Exportación a Excel
```php
// Abstract Export Class
abstract class Export implements FromCollection, ShouldAutoSize, ShouldQueue, WithHeadings, WithMapping
{
    use Exportable;

    public function map($model): array
    {
        $map = [];
        $date_fields = ['paid_at', 'invoiced_at', 'billed_at', 'due_at', 'issued_at'];
        $evil_chars = ['=', '+', '-', '@']; // Prevenir inyección CSV

        foreach ($this->fields as $field) {
            $value = $model->$field;

            // Formatear fechas para Excel
            if (in_array($field, $date_fields)) {
                $value = ExcelDate::PHPToExcel(Date::parse($value)->format('Y-m-d'));
            }

            // Prevenir inyección CSV
            if (Str::startsWith($value, $evil_chars)) {
                $value = "'" . $value;
            }

            $map[] = $value;
        }

        return $map;
    }
}
```

#### Tipos de Exportación
```php
// Exportar Facturas
class InvoiceExport extends Export
{
    public function fields(): array
    {
        return [
            'document_number',
            'issued_at',
            'due_at',
            'contact_name',
            'amount',
            'status',
            'created_at'
        ];
    }
}

// Exportar Contactos
class ContactExport extends Export
{
    public function fields(): array
    {
        return [
            'name',
            'email',
            'phone',
            'address',
            'tax_number',
            'type',
            'enabled'
        ];
    }
}
```

## 9. SISTEMA DE MÓDULOS Y EXTENSIBILIDAD

### 9.1 Estructura de Módulos

#### Configuración de Módulos
```php
// config/module.php
'paths' => [
    'modules' => base_path('modules'),
    'assets' => public_path('modules'),
    'migration' => base_path('database/migrations'),
    'generator' => [
        'config' => ['path' => 'Config', 'generate' => true],
        'command' => ['path' => 'Console', 'generate' => true],
        'migration' => ['path' => 'Database/Migrations', 'generate' => true],
        'seeder' => ['path' => 'Database/Seeders', 'generate' => true],
        'factory' => ['path' => 'Database/Factories', 'generate' => true],
        'model' => ['path' => 'Models', 'generate' => true],
        'controller' => ['path' => 'Http/Controllers', 'generate' => true],
        'filter' => ['path' => 'Http/Middleware', 'generate' => true],
        'request' => ['path' => 'Http/Requests', 'generate' => true],
        'provider' => ['path' => 'Providers', 'generate' => true],
        'assets' => ['path' => 'Resources/assets', 'generate' => true],
        'lang' => ['path' => 'Resources/lang', 'generate' => true],
        'views' => ['path' => 'Resources/views', 'generate' => true],
        'test' => ['path' => 'Tests', 'generate' => true],
        'repository' => ['path' => 'Repositories', 'generate' => true],
        'event' => ['path' => 'Events', 'generate' => true],
        'listener' => ['path' => 'Listeners', 'generate' => true],
        'policies' => ['path' => 'Policies', 'generate' => true],
        'rules' => ['path' => 'Rules', 'generate' => true],
        'jobs' => ['path' => 'Jobs', 'generate' => true],
        'emails' => ['path' => 'Emails', 'generate' => true],
        'notifications' => ['path' => 'Notifications', 'generate' => true],
    ],
];
```

#### Estructura de un Módulo
```
modules/
├── ModuleName/
│   ├── Config/
│   │   └── config.php
│   ├── Console/
│   │   └── Commands/
│   ├── Database/
│   │   ├── Migrations/
│   │   ├── Seeders/
│   │   └── Factories/
│   ├── Http/
│   │   ├── Controllers/
│   │   ├── Middleware/
│   │   └── Requests/
│   ├── Models/
│   ├── Providers/
│   │   └── ModuleServiceProvider.php
│   ├── Resources/
│   │   ├── assets/
│   │   ├── lang/
│   │   └── views/
│   ├── Routes/
│   │   ├── web.php
│   │   └── api.php
│   ├── Tests/
│   ├── composer.json
│   └── module.json
```

### 9.2 Registro de Módulos

#### module.json
```json
{
    "name": "ModuleName",
    "alias": "modulename",
    "description": "Descripción del módulo",
    "keywords": [],
    "priority": 0,
    "providers": [
        "Modules\\ModuleName\\Providers\\ModuleServiceProvider"
    ],
    "aliases": {},
    "files": [],
    "requires": []
}
```

#### Service Provider del Módulo
```php
// Providers/ModuleServiceProvider.php
class ModuleServiceProvider extends ServiceProvider
{
    protected $moduleName = 'ModuleName';
    protected $moduleNameLower = 'modulename';

    public function boot()
    {
        $this->registerTranslations();
        $this->registerConfig();
        $this->registerViews();
        $this->loadMigrationsFrom(module_path($this->moduleName, 'Database/Migrations'));
    }

    public function register()
    {
        $this->app->register(RouteServiceProvider::class);
    }

    protected function registerConfig()
    {
        $this->publishes([
            module_path($this->moduleName, 'Config/config.php') => config_path($this->moduleNameLower . '.php'),
        ], 'config');

        $this->mergeConfigFrom(
            module_path($this->moduleName, 'Config/config.php'), $this->moduleNameLower
        );
    }
}
```

## 10. SISTEMA DE INTERNACIONALIZACIÓN

### 10.1 Estructura de Traducciones

#### Archivos de Idioma
```php
// resources/lang/es/messages.php
return [
    'success' => [
        'added'     => '¡:type agregado!',
        'updated'   => '¡:type actualizado!',
        'deleted'   => '¡:type eliminado!',
        'imported'  => '¡:type importado!',
        'exported'  => '¡:type exportado!',
        'enabled'   => '¡:type habilitado!',
        'disabled'  => '¡:type deshabilitado!',
    ],

    'error' => [
        'not_user_company'  => 'Error: No tienes permisos para administrar esta empresa.',
        'customer'          => 'Error: No puedes crear un usuario que ya es cliente.',
        'no_file'           => 'Error: No se seleccionó ningún archivo.',
        'last_category'     => 'Error: No puedes eliminar la última categoría :type.',
    ],

    'warning' => [
        'deleted'           => 'Advertencia: No puedes eliminar <b>:name</b> porque tiene :text relacionados.',
        'disabled'          => 'Advertencia: No puedes deshabilitar <b>:name</b> porque tiene :text relacionados.',
        'disable_code'      => 'Advertencia: No puedes deshabilitar o cambiar la moneda de <b>:name</b> porque tiene :text relacionados.',
    ],
];
```

#### Traducciones de Interfaz
```php
// resources/lang/es/general.php
return [
    'dashboard'         => 'Panel de Control',
    'banking'           => 'Banca',
    'sales'             => 'Ventas',
    'purchases'         => 'Compras',
    'items'             => 'Productos',
    'reports'           => 'Reportes',
    'settings'          => 'Configuración',

    'accounts'          => 'Cuentas',
    'transactions'      => 'Transacciones',
    'transfers'         => 'Transferencias',
    'reconciliations'   => 'Conciliaciones',

    'invoices'          => 'Facturas',
    'revenues'          => 'Ingresos',
    'customers'         => 'Clientes',

    'bills'             => 'Facturas de Compra',
    'payments'          => 'Pagos',
    'vendors'           => 'Proveedores',
];
```

### 10.2 Uso de Traducciones en Blade
```html
<!-- Uso básico -->
<h1>{{ trans('general.dashboard') }}</h1>

<!-- Con parámetros -->
<p>{{ trans('messages.success.added', ['type' => trans('general.invoice')]) }}</p>

<!-- Pluralización -->
<span>{{ trans_choice('general.invoices', $count) }}</span>

<!-- En JavaScript -->
<script>
    var translations = {
        'save': '{{ trans("general.save") }}',
        'cancel': '{{ trans("general.cancel") }}',
        'confirm_delete': '{{ trans("general.confirm_delete") }}'
    };
</script>
```

### 10.3 Idiomas Soportados
- **Español (es)** - Idioma principal
- **Inglés (en)** - Idioma por defecto
- **Francés (fr)**
- **Alemán (de)**
- **Italiano (it)**
- **Portugués (pt-BR)**
- **Chino Simplificado (zh-CN)**
- **Chino Tradicional (zh-TW)**
- **Turco (tr-TR)**
- **Árabe (ar)**
- **Ruso (ru)**

## 11. SISTEMA DE TESTING Y CALIDAD

### 11.1 Estructura de Testing

#### Configuración PHPUnit
```xml
<!-- phpunit.xml -->
<phpunit bootstrap="vendor/autoload.php"
         colors="true"
         processIsolation="false"
         stopOnFailure="false"
         cacheResult="false">
    <testsuites>
        <testsuite name="Feature">
            <directory suffix="Test.php">./tests/Feature</directory>
        </testsuite>
        <testsuite name="Unit">
            <directory suffix="Test.php">./tests/Unit</directory>
        </testsuite>
    </testsuites>

    <php>
        <server name="APP_ENV" value="testing"/>
        <server name="BCRYPT_ROUNDS" value="4"/>
        <server name="CACHE_DRIVER" value="array"/>
        <server name="DB_CONNECTION" value="sqlite"/>
        <server name="DB_DATABASE" value=":memory:"/>
        <server name="MAIL_MAILER" value="array"/>
        <server name="QUEUE_CONNECTION" value="sync"/>
        <server name="SESSION_DRIVER" value="array"/>
    </php>
</phpunit>
```

#### Base Test Case
```php
// tests/TestCase.php
abstract class TestCase extends BaseTestCase
{
    use CreatesApplication, RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        $this->artisan('company:seed');
        $this->artisan('migrate:fresh');
        $this->artisan('db:seed', ['--class' => 'Database\\Seeds\\TestCompany']);
    }

    protected function signIn($user = null)
    {
        $user = $user ?: $this->createUser();

        $this->actingAs($user);

        return $user;
    }

    protected function createUser($attributes = [])
    {
        return User::factory()->create($attributes);
    }

    protected function createCompany($attributes = [])
    {
        return Company::factory()->create($attributes);
    }
}
```

#### Feature Tests
```php
// tests/Feature/InvoiceTest.php
class InvoiceTest extends FeatureTestCase
{
    /** @test */
    public function user_can_create_invoice()
    {
        $this->signIn();

        $customer = Contact::factory()->customer()->create();
        $item = Item::factory()->create();

        $response = $this->post(route('invoices.store'), [
            'contact_id' => $customer->id,
            'issued_at' => now()->format('Y-m-d'),
            'due_at' => now()->addDays(30)->format('Y-m-d'),
            'items' => [
                [
                    'item_id' => $item->id,
                    'name' => $item->name,
                    'quantity' => 2,
                    'price' => 100.00,
                ]
            ]
        ]);

        $response->assertRedirect();
        $this->assertDatabaseHas('documents', [
            'type' => 'invoice',
            'contact_id' => $customer->id,
        ]);
    }

    /** @test */
    public function user_can_view_invoice_pdf()
    {
        $this->signIn();

        $invoice = Document::factory()->invoice()->create();

        $response = $this->get(route('invoices.pdf', $invoice));

        $response->assertStatus(200);
        $response->assertHeader('Content-Type', 'application/pdf');
    }
}
```

#### Unit Tests
```php
// tests/Unit/DocumentTest.php
class DocumentTest extends TestCase
{
    /** @test */
    public function document_calculates_total_correctly()
    {
        $document = Document::factory()->create();

        DocumentItem::factory()->create([
            'document_id' => $document->id,
            'quantity' => 2,
            'price' => 50.00,
            'total' => 100.00
        ]);

        DocumentItem::factory()->create([
            'document_id' => $document->id,
            'quantity' => 1,
            'price' => 25.00,
            'total' => 25.00
        ]);

        $this->assertEquals(125.00, $document->fresh()->sub_total);
    }
}
```

### 11.2 CI/CD Pipeline

#### GitHub Actions
```yaml
# .github/workflows/tests.yml
name: Tests

on:
  push:
    branches: [ master, develop ]
  pull_request:
    branches: [ master ]

jobs:
  test:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        php-version: [8.1, 8.2, 8.3]

    steps:
    - uses: actions/checkout@v3

    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: ${{ matrix.php-version }}
        extensions: dom, curl, libxml, mbstring, zip, pcntl, pdo, sqlite, pdo_sqlite
        coverage: none

    - name: Cache Composer packages
      id: composer-cache
      uses: actions/cache@v3
      with:
        path: vendor
        key: ${{ runner.os }}-php-${{ hashFiles('**/composer.lock') }}
        restore-keys: |
          ${{ runner.os }}-php-

    - name: Install dependencies
      run: composer install --prefer-dist --no-progress

    - name: Copy environment file
      run: cp .env.testing .env

    - name: Generate application key
      run: php artisan key:generate

    - name: Run tests
      run: vendor/bin/phpunit --parallel
```

### 11.3 Performance Testing
```php
// tests/Feature/Performance/N1QueryOptimizationTest.php
class N1QueryOptimizationTest extends TestCase
{
    /** @test */
    public function invoice_index_does_not_have_n1_queries()
    {
        $this->signIn();

        // Crear datos de prueba
        Document::factory()->invoice()->count(10)->create();

        // Habilitar query logging
        DB::enableQueryLog();

        $response = $this->get(route('invoices.index'));

        $queries = DB::getQueryLog();

        // Verificar que no hay más de X queries
        $this->assertLessThan(5, count($queries));

        $response->assertStatus(200);
    }
}
```

## 12. CONFIGURACIÓN Y DEPLOYMENT

### 12.1 Configuración del Servidor

#### Nginx Configuration
```nginx
# nginx.example.com.conf
server {
    listen 80;
    listen [::]:80;
    server_name example.com www.example.com;
    root /var/www/html/public;

    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-Content-Type-Options "nosniff";
    add_header X-XSS-Protection "1; mode=block";
    add_header Referrer-Policy "strict-origin-when-cross-origin";
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self'; media-src 'self'; object-src 'none'; child-src 'self'; frame-ancestors 'none'; form-action 'self'; base-uri 'self';";

    index index.php;

    charset utf-8;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location = /favicon.ico { access_log off; log_not_found off; }
    location = /robots.txt  { access_log off; log_not_found off; }

    error_page 404 /index.php;

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    location ~ /\.(?!well-known).* {
        deny all;
    }

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private auth;
    gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml+rss;
    gzip_disable "MSIE [1-6]\.";
}
```

#### PHP Configuration
```ini
# php.ini optimizations
memory_limit = 512M
max_execution_time = 300
max_input_vars = 3000
upload_max_filesize = 100M
post_max_size = 100M
max_file_uploads = 20

# OPcache settings
opcache.enable=1
opcache.memory_consumption=256
opcache.interned_strings_buffer=12
opcache.max_accelerated_files=4000
opcache.revalidate_freq=60
opcache.fast_shutdown=1
```

### 12.2 Variables de Entorno

#### Archivo .env
```bash
# Aplicación
APP_NAME="Sistema Contable"
APP_ENV=production
APP_KEY=base64:generated_key_here
APP_DEBUG=false
APP_URL=https://example.com

# Base de Datos
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=accounting_db
DB_USERNAME=db_user
DB_PASSWORD=secure_password

# Cache y Sesiones
CACHE_DRIVER=redis
SESSION_DRIVER=redis
QUEUE_CONNECTION=redis

# Redis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

# Email
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="${APP_NAME}"

# Filesystem
FILESYSTEM_DISK=local

# Firewall
FIREWALL_ENABLED=true
FIREWALL_MIDDLEWARE=firewall.all

# Logging
LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=error
```

### 12.3 Comandos de Deployment

#### Script de Deployment
```bash
#!/bin/bash
# deploy.sh

echo "🚀 Iniciando deployment..."

# Actualizar código
git pull origin main

# Instalar dependencias
composer install --no-dev --optimize-autoloader

# Instalar dependencias de Node.js
npm ci --production

# Compilar assets
npm run production

# Ejecutar migraciones
php artisan migrate --force

# Limpiar y optimizar cache
php artisan config:cache
php artisan route:cache
php artisan view:cache
php artisan event:cache

# Optimizar autoloader
composer dump-autoload --optimize

# Reiniciar servicios
sudo systemctl reload php8.1-fpm
sudo systemctl reload nginx

# Reiniciar workers de queue
php artisan queue:restart

echo "✅ Deployment completado!"
```

#### Comandos de Mantenimiento
```bash
# Backup de base de datos
php artisan backup:run

# Limpiar archivos temporales
php artisan app:clean-temp

# Optimizar base de datos
php artisan db:optimize

# Generar sitemap
php artisan sitemap:generate

# Verificar estado del sistema
php artisan system:check
```

## 13. PLAN DE DESARROLLO DETALLADO

### 13.1 Fase 1: Configuración Base (Semanas 1-3)

#### Semana 1: Setup del Proyecto
- [ ] Configurar entorno de desarrollo (Laravel 10.0, PHP 8.1+)
- [ ] Configurar base de datos MySQL
- [ ] Instalar y configurar dependencias principales
- [ ] Configurar sistema de versionado Git
- [ ] Configurar entorno de testing

#### Semana 2: Autenticación y Multi-tenancy
- [ ] Implementar sistema de autenticación Laravel Sanctum
- [ ] Configurar middleware de identificación de empresa
- [ ] Implementar sistema de roles y permisos (Laratrust)
- [ ] Crear migraciones base de usuarios y empresas
- [ ] Implementar registro e invitación de usuarios

#### Semana 3: Estructura Base
- [ ] Crear estructura de layouts Blade
- [ ] Configurar Tailwind CSS y compilación de assets
- [ ] Implementar componentes Vue.js base
- [ ] Configurar sistema de rutas y middleware
- [ ] Implementar dashboard básico

### 13.2 Fase 2: Módulos Core (Semanas 4-8)

#### Semana 4: Gestión de Empresas y Configuración
- [ ] Implementar CRUD de empresas
- [ ] Sistema de configuración multi-tenant
- [ ] Gestión de monedas y tipos de cambio
- [ ] Configuración de impuestos
- [ ] Sistema de categorías

#### Semana 5: Gestión de Contactos
- [ ] CRUD de clientes y proveedores
- [ ] Sistema de direcciones y contactos
- [ ] Importación/exportación de contactos
- [ ] Validaciones y reglas de negocio
- [ ] API REST para contactos

#### Semana 6: Gestión de Productos
- [ ] CRUD de productos y servicios
- [ ] Sistema de categorías de productos
- [ ] Gestión de precios y descuentos
- [ ] Control de inventario básico
- [ ] Importación/exportación de productos

#### Semana 7: Sistema Bancario
- [ ] CRUD de cuentas bancarias
- [ ] Gestión de transacciones
- [ ] Sistema de transferencias
- [ ] Conciliación bancaria
- [ ] Reportes de flujo de efectivo

#### Semana 8: Testing y Optimización Fase 2
- [ ] Tests unitarios para módulos core
- [ ] Tests de integración
- [ ] Optimización de consultas N+1
- [ ] Refactoring y limpieza de código
- [ ] Documentación técnica

### 13.3 Fase 3: Sistema de Documentos (Semanas 9-13)

#### Semana 9: Base de Documentos
- [ ] Modelo unificado de documentos
- [ ] Sistema de numeración automática
- [ ] Estados y workflow de documentos
- [ ] Relaciones con contactos y productos
- [ ] Validaciones de negocio

#### Semana 10: Facturas de Venta
- [ ] CRUD completo de facturas
- [ ] Cálculo automático de totales e impuestos
- [ ] Sistema de descuentos
- [ ] Generación de transacciones
- [ ] Estados de pago

#### Semana 11: Facturas de Compra
- [ ] CRUD de facturas de compra
- [ ] Gestión de pagos a proveedores
- [ ] Control de vencimientos
- [ ] Aprobación de facturas
- [ ] Reportes de compras

#### Semana 12: Templates y PDF
- [ ] Sistema de templates de documentos
- [ ] Generación de PDF con DomPDF
- [ ] Templates responsive para impresión
- [ ] Personalización de templates
- [ ] Sistema de impresión

#### Semana 13: Portal del Cliente
- [ ] Layout del portal del cliente
- [ ] Visualización de facturas
- [ ] Sistema de pagos online
- [ ] Descarga de documentos
- [ ] Notificaciones por email

### 13.4 Fase 4: Reportes y Analytics (Semanas 14-16)

#### Semana 14: Reportes Financieros
- [ ] Reporte de ingresos y gastos
- [ ] Estado de pérdidas y ganancias
- [ ] Balance general
- [ ] Flujo de efectivo
- [ ] Reportes por período

#### Semana 15: Reportes de Gestión
- [ ] Reportes de ventas por cliente
- [ ] Reportes de compras por proveedor
- [ ] Análisis de productos más vendidos
- [ ] Reportes de impuestos
- [ ] Dashboard con métricas

#### Semana 16: Exportación y Analytics
- [ ] Exportación a Excel/CSV
- [ ] Gráficos interactivos
- [ ] Comparativas por períodos
- [ ] Proyecciones y tendencias
- [ ] Reportes personalizables

### 13.5 Fase 5: Módulos Avanzados (Semanas 17-20)

#### Semana 17: Sistema de Módulos
- [ ] Arquitectura de módulos extensibles
- [ ] Marketplace de módulos
- [ ] Sistema de instalación/desinstalación
- [ ] API para desarrolladores
- [ ] Documentación de módulos

#### Semana 18: Características Avanzadas
- [ ] Sistema de documentos recurrentes
- [ ] Recordatorios automáticos
- [ ] Workflow de aprobaciones
- [ ] Firma digital de documentos
- [ ] Integración con APIs externas

#### Semana 19: Optimización y Performance
- [ ] Optimización de base de datos
- [ ] Implementación de cache Redis
- [ ] Optimización de assets
- [ ] Lazy loading y paginación
- [ ] Monitoreo de performance

#### Semana 20: Seguridad y Firewall
- [ ] Implementación de firewall avanzado
- [ ] Protección contra ataques
- [ ] Auditoría de seguridad
- [ ] Logs de seguridad
- [ ] Backup automático

### 13.6 Fase 6: Testing y Deployment (Semanas 21-24)

#### Semana 21: Testing Completo
- [ ] Suite completa de tests unitarios
- [ ] Tests de integración end-to-end
- [ ] Tests de performance
- [ ] Tests de seguridad
- [ ] Cobertura de código 90%+

#### Semana 22: Documentación
- [ ] Documentación de usuario final
- [ ] Documentación técnica completa
- [ ] Guías de instalación
- [ ] API documentation
- [ ] Videos tutoriales

#### Semana 23: Deployment y DevOps
- [ ] Configuración de servidores
- [ ] CI/CD pipeline completo
- [ ] Monitoreo y alertas
- [ ] Backup y recuperación
- [ ] SSL y certificados

#### Semana 24: Launch y Soporte
- [ ] Deployment a producción
- [ ] Migración de datos
- [ ] Training del equipo
- [ ] Soporte post-launch
- [ ] Monitoreo y mantenimiento

## 14. ESTIMACIÓN DE RECURSOS

### 14.1 Equipo Requerido
- **1 Senior Full-Stack Developer** (Laravel + Vue.js)
- **1 Frontend Developer** (Vue.js + Tailwind CSS)
- **1 Backend Developer** (Laravel + MySQL)
- **1 QA Engineer** (Testing + Automation)
- **1 DevOps Engineer** (Deployment + Infrastructure)
- **1 Project Manager** (Coordinación + Planning)

### 14.2 Tiempo Total Estimado
- **Duración:** 24 semanas (6 meses)
- **Esfuerzo:** ~144 semanas-persona
- **Horas totales:** ~5,760 horas

### 14.3 Tecnologías y Herramientas
- **Backend:** Laravel 10.0, PHP 8.1+, MySQL 8.0
- **Frontend:** Vue.js 2.7.14, Tailwind CSS, Element UI
- **Testing:** PHPUnit, Laravel Dusk, Jest
- **DevOps:** Docker, Nginx, Redis, GitHub Actions
- **Monitoring:** Laravel Telescope, Sentry, New Relic
